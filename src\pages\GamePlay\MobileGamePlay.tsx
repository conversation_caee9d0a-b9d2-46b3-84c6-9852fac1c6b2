import { useState, useEffect, useRef } from "react";
import { use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import {
  ArrowLeft,
  Maximize,
  Minimize,
  Shield,
  Trophy,
  User,
  Menu,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useGame } from "@/contexts/GameContext";
import { useUpdateScore } from "@/hooks/use-game-api";
import { toast } from "@/hooks/use-toast";
import GameRenderer from "@/components/games/GameRenderer";
import styles from "./GamePlay.module.css";

const MobileGamePlay = () => {
  const { gameId } = useParams();
  const { authToken, userData, selectedGame, gameStats, hasAuthToken } =
    useGame();

  const [isFullscreen, setIsFullscreen] = useState(false);
  const [gameScore, setGameScore] = useState(0);
  const [isGameLoaded, setIsGameLoaded] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // API hooks
  const updateScoreMutation = useUpdateScore();

  // Use selected game from context or fallback
  const game = selectedGame && {
    id: selectedGame._id,
    title: selectedGame.title,
    description: selectedGame.description,
    blockchain: selectedGame.blockchain,
    category: selectedGame.category,
    image: selectedGame.image,
    gameUrl: selectedGame.game_url,
  };

  // Player stats - use game stats from context or fallback
  const currentGameStats = gameStats && gameId ? gameStats[gameId] : null;
  const playerStats = currentGameStats
    ? {
        bestScore: currentGameStats.best_score,
        gamesPlayed: currentGameStats.games_played,
        totalEarned: currentGameStats.total_score,
      }
    : {
        bestScore: 0,
        gamesPlayed: 0,
        totalEarned: 0,
      };

  // Handle window resize in fullscreen mode
  useEffect(() => {
    const handleResize = () => {
      if (isFullscreen && iframeRef.current) {
        // Force iframe to resize properly in fullscreen
        iframeRef.current.style.width = "100vw";
        iframeRef.current.style.height = "100vh";
      }
    };

    if (isFullscreen) {
      window.addEventListener("resize", handleResize);
      // Initial resize
      handleResize();
    }

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [isFullscreen]);

  // Handle score update
  const handleScoreUpdate = (newScore: number) => {
    if (!authToken || !gameId) {
      toast({
        title: "Cannot Save Score",
        description: "Authentication is required to save your progress.",
        variant: "destructive",
      });
      return;
    }

    updateScoreMutation.mutate(
      { gameId, score: newScore, authToken },
      {
        onSuccess: (data) => {
          toast({
            title: "Score Updated!",
            description: `Best: ${data.best_score} | Games played: ${data.games_played}`,
          });
        },
        onError: (error) => {
          toast({
            title: "Failed to Save Score",
            description: "Please try again later.",
            variant: "destructive",
          });
          console.error("Score update error:", error);
        },
      }
    );
  };

  // HANDLE IFRAME LOAD AND FOCUS
  const handleIframeLoad = () => {
    setIsGameLoaded(true);

    // Focus the iframe to make it interactive immediately
    if (iframeRef.current) {
      try {
        // Focus the iframe element
        iframeRef.current.focus();

        // Also try to focus the content window if possible
        if (iframeRef.current.contentWindow) {
          iframeRef.current.contentWindow.focus();
        }

        console.log("🎮 Game loaded and focused");
      } catch (error) {
        console.log("⚠️ Could not focus iframe:", error);
      }
    }
  };

  // 🎯 EXTRACT SCORE WHEN GAME IS OVER
  useEffect(() => {
    const checkGameOver = () => {
      if (!iframeRef.current?.contentWindow || !isGameLoaded) return;

      try {
        // 🔍 ACCESS GAME VARIABLES DIRECTLY FROM IFRAME
        const gameWindow = iframeRef.current.contentWindow as Window & {
          score?: number;
          document?: Document;
        };

        // ⭐ CHECK IF GAME OVER SCREEN IS VISIBLE
        const gameOverElement = gameWindow.document?.getElementsByClassName(
          "endlessrunner-gameover-background"
        )[0] as HTMLElement;
        const isGameOver = gameOverElement?.style.display === "block";

        // 📊 EXTRACT SCORE ONLY WHEN GAME IS OVER
        if (isGameOver) {
          const currentScore = gameWindow.score || 0;
          if (currentScore !== gameScore) {
            setGameScore(currentScore);
            console.log("🏁 Game ended with score:", currentScore);

            // Update score via API
            if (currentScore > 0) {
              handleScoreUpdate(currentScore);
            }
          }
        }
      } catch (error) {
        // Game iframe not ready yet or cross-origin restrictions
        console.log("⏳ Waiting for game to load...");
      }
    };

    // 🔄 CHECK FOR GAME OVER EVERY 1 SECOND
    const interval = setInterval(checkGameOver, 1000);

    return () => clearInterval(interval);
  }, [gameScore, isGameLoaded, authToken]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);

    // Focus iframe after toggling fullscreen with a longer delay for fullscreen mode
    setTimeout(
      () => {
        if (iframeRef.current) {
          try {
            iframeRef.current.focus();
            if (iframeRef.current.contentWindow) {
              iframeRef.current.contentWindow.focus();
            }

            // Force iframe to resize and scale properly in fullscreen
            if (iframeRef.current) {
              iframeRef.current.style.width = "100%";
              iframeRef.current.style.height = "100%";

              // Additional fullscreen scaling fixes
              if (isFullscreen) {
                iframeRef.current.style.transform = "none";
                iframeRef.current.style.scale = "1";
                iframeRef.current.style.objectFit = "cover";
              }
            }
          } catch (error) {
            console.log(
              "⚠️ Could not focus iframe after fullscreen toggle:",
              error
            );
          }
        }
      },
      isFullscreen ? 200 : 100
    ); // Longer delay for entering fullscreen
  };

  // Check if game exists
  if (!game) {
    return (
      <div className="min-h-screen relative overflow-hidden pt-24">
        <div className="fixed inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30 pointer-events-none"></div>
        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-8 text-center max-w-md mx-auto">
            <div className="text-4xl mb-4">🎮</div>
            <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Game Not Found
            </h2>
            <p className="text-secondary-text mb-6">
              The game you're looking for doesn't exist.
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-gaming-gold to-legendary-orange text-deep-space font-bold"
            >
              <Link to="/games">Back to Games</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="min-h-screen relative overflow-hidden pt-24">
        <div className="fixed inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30 pointer-events-none"></div>
        <div className="fixed inset-0 digital-grid opacity-10 pointer-events-none"></div>

        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-8 text-center max-w-md mx-auto">
            <div className="mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-accent-cyan/20 to-xp-purple/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-10 w-10 text-accent-cyan" />
              </div>
              <div className="text-4xl mb-3">🎮</div>
            </div>
            <h2 className="text-2xl font-bold mb-3 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Connect Your Account
            </h2>
            <p className="text-secondary-text mb-4 leading-relaxed text-sm">
              You need to connect your account to play games and earn epic
              rewards in the gaming multiverse.
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-gaming-gold to-legendary-orange text-deep-space font-bold px-6 py-2 hover:scale-105 transition-all duration-300"
            >
              <Link to="/games">Back to Gaming Hub</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`${
        isFullscreen
          ? `${styles["fullscreen-container"]} fixed inset-0 z-50 w-screen h-screen`
          : "min-h-screen"
      } relative overflow-hidden bg-deep-space`}
    >
      {!isFullscreen && (
        <div>
          {/* Background */}
          <div className="fixed inset-0 overflow-hidden pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
            <div className="absolute inset-0 digital-grid opacity-5"></div>
          </div>

          {/* Mobile Game Header */}
          <div className="relative z-10 bg-gradient-to-r from-card-dark/95 to-surface-dark/90 backdrop-blur-xl border-b border-border-light/30 pt-24 pb-4 px-4">
            <div className="max-w-7xl mx-auto">
              {/* Top Row - Back Button and Game Icon */}
              <div className="flex items-center justify-between mb-4">
                <Button
                  asChild
                  variant="ghost"
                  size="sm"
                  className="hover:bg-accent-cyan/10 hover:text-accent-cyan transition-all duration-300 p-0"
                >
                  <Link to="/games">
                    <ArrowLeft className="h-4 w-4" />
                    Back
                  </Link>
                </Button>
                <div className="text-4xl">{game.image}</div>
                <Button
                  onClick={() => setShowStats(!showStats)}
                  variant="ghost"
                  size="sm"
                  className="hover:bg-accent-cyan/10 hover:text-accent-cyan transition-all duration-300"
                >
                  {showStats ? (
                    <X className="h-4 w-4" />
                  ) : (
                    <Menu className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* Game Info */}
              <div className="text-center flex flex-row justify-between align-center">
                <h1 className="text-xl font-bold bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
                  {game.title}
                </h1>
                {/* <div className="flex items-center justify-center gap-2">
                  <span className="px-2 py-1 text-xs font-bold bg-binance-gold/20 text-binance-gold rounded-full">
                    {game.blockchain}
                  </span>
                  <span className="px-2 py-1 text-xs font-medium bg-xp-purple/20 text-xp-purple rounded-full">
                    {game.category}
                  </span>
                </div>
                {gameScore > 0 && (
                  <div className="mt-3 p-2 bg-accent-cyan/10 border border-accent-cyan/30 rounded-lg">
                    <p className="text-xs text-accent-cyan font-medium">
                      🏁 Final Score:{" "}
                      <span className="font-bold text-sm">
                        {gameScore.toLocaleString()}
                      </span>
                    </p>
                  </div>
                )} */}
                <div className="flex items-center justify-center">
                  <Button
                    onClick={toggleFullscreen}
                    variant="outline"
                    size="sm"
                    className="border-accent-cyan text-accent-cyan hover:bg-accent-cyan/10"
                  >
                    {isFullscreen ? (
                      <Minimize className="h-4 w-4 mr-2" />
                    ) : (
                      <Maximize className="h-4 w-4 mr-2" />
                    )}
                    {isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
                  </Button>
                </div>
              </div>

              {/* Game Controls */}
            </div>
          </div>
        </div>
      )}

      {/* Game Container */}
      <div
        className={`relative z-10 ${
          isFullscreen ? "h-screen" : "h-[calc(100vh-200px)]"
        }`}
      >
        <div className="h-full w-full relative flex">
          {/* Main Game Area */}
          <div className={`relative ${showStats ? "flex-1" : "w-full"}`}>
            <GameRenderer
              gameId={gameId || ""}
              gameUrl={game.gameUrl}
              gameTitle={game.title}
              isFullscreen={isFullscreen}
              onGameEnd={handleScoreUpdate}
              onLoad={handleIframeLoad}
            />
          </div>

          {/* Player Stats Sidebar - Mobile Optimized */}
          {showStats && !isFullscreen && (
            <div className="w-64 bg-gradient-to-b from-card-dark/95 to-surface-dark/90 backdrop-blur-xl border-l border-border-light/30 p-4 space-y-4 overflow-y-auto">
              {/* Player Performance */}
              <div className="bg-gradient-to-br from-surface-dark/80 to-border-light/40 backdrop-blur-sm border border-border-light/30 rounded-xl p-3">
                <h3 className="font-bold mb-3 flex items-center text-base">
                  <Trophy className="mr-2 h-4 w-4 text-gaming-gold" />
                  Your Performance
                </h3>
                {!hasAuthToken ? (
                  <div className="text-center py-3">
                    <User className="h-6 w-6 text-accent-cyan mx-auto mb-2" />
                    <p className="text-xs text-secondary-text">
                      Connect your account to track stats
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-secondary-text text-sm">
                        Best
                      </span>
                      <span className="font-bold text-gaming-gold text-sm">
                        {playerStats.bestScore.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-secondary-text text-sm">
                        Games Played
                      </span>
                      <span className="font-bold text-accent-cyan text-sm">
                        {playerStats.gamesPlayed}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-secondary-text text-sm">
                        Total Earned
                      </span>
                      <span className="font-bold text-success-green text-sm">
                        {playerStats.totalEarned.toLocaleString()} JQ
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Fullscreen overlay controls */}
          {isFullscreen && (
            <div
              className={`${styles["fullscreen-controls"]} absolute top-4 right-4 z-50`}
            >
              <Button
                onClick={toggleFullscreen}
                variant="outline"
                size="sm"
                className="bg-black/50 border-accent-cyan text-accent-cyan hover:bg-accent-cyan/10 backdrop-blur-sm"
              >
                <Minimize className="h-4 w-4 mr-2" />
                Exit Fullscreen
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MobileGamePlay;
