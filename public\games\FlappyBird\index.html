<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Project 001</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="game-screen">
            <canvas id="game" width="300" height="500"></canvas>
            <p id="description" class="game-description">Press 'spacebar' or 'click' to begin</p>

            <!-- Custom Game Over Overlay -->
            <div id="custom-game-over" class="custom-game-over-overlay" style="display: none;">
                <div class="game-over-content">
                    <h2 class="game-over-title">Game Over</h2>
                    <div class="score-container">
                        <div class="score-item">
                            <span class="score-label">Score</span>
                            <span id="final-score" class="score-value">0</span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Best</span>
                            <span id="best-score" class="score-value">0</span>
                        </div>
                    </div>
                    <button id="restart-btn" class="restart-button">Play Again</button>
                </div>
            </div>
        </div>
    </div>
    <script src="main.js"></script>
</body>
</html>
