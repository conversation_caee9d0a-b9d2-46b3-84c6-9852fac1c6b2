@import url('https://fonts.googleapis.com/css2?family=Carter+One&display=swap');
* {
    font-family: helvetica, sans-serif;
    -webkit-user-select: none;
    -moz-user-select: none;
}
body {
    background-color: rgba(0,0,0, .95);
    height: 100vh;
    margin: 0;
}

/********************
* video game screen *
********************/
.game-title h1 {
/***** Flappy Bird *****/
    text-align: center;
    font-family: 'Carter One', cursive;
    color: rgb(251, 176, 37);
    font-size: 3em;
    line-height: 150%;
    letter-spacing: 4px;
    text-shadow: 2px 4px rgba(250,250,250, 1);
    margin: 0;
    position: relative;
    top: 10px;
}
.game-screen {
    /* background-color: rgb(69, 184, 194); */
    width: 300px;
    height: 500px;
    margin: 0 auto;
    position: relative;
    top: 50px;
}
    canvas {
        background-color: 
        black
        /* #00bbc4 */
        /* #d2ce89 */
        ;
        display: block;
        width: 300px;
        height: 500px;
        margin: 0 auto;
        border-radius: 10px;
    }
    .game-description {
        color: #f0f0f0;
        line-height: 2em;
        text-align: center;
    }

/* Custom Game Over Overlay */
.custom-game-over-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 10px;
}

.game-over-content {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border: 3px solid #f39c12;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    min-width: 200px;
}

.game-over-title {
    color: #f39c12;
    font-family: 'Carter One', cursive;
    font-size: 2.2em;
    margin: 0 0 20px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.score-container {
    margin: 20px 0;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.score-label {
    color: #ecf0f1;
    font-weight: bold;
    font-size: 1.1em;
}

.score-value {
    color: #f39c12;
    font-weight: bold;
    font-size: 1.3em;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.restart-button {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1.1em;
    font-weight: bold;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.restart-button:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.restart-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(231, 76, 60, 0.3);
}